# **App Name**: SecurePass

## Core Features:

- API Key Management: Securely store and manage API keys for various AI models.
- API Key Actions: Add, update, and delete API keys with active/inactive status control.
- API Key Search: Search and filter API keys by AI model name.
- Password Management: Securely store and manage general passwords (e.g., Instagram, Gmail).
- Password Actions: Add, update, and delete passwords with active/inactive status control.
- Password Search: Search and filter passwords by app/platform name or username.
- User Authentication: User Authentication using Firebase Authentication.

## Style Guidelines:

- Primary color: Deep Indigo (#3F51B5) for a sense of security and reliability.
- Background color: <PERSON> Gray (#F5F5F5) for a clean and modern dashboard appearance.
- Accent color: Teal (#009688) for interactive elements and highlights to provide a sense of calm.
- Body and headline font: 'Inter', a sans-serif font, known for its clean and versatile appearance.
- Use consistent and clear icons for each AI model and platform to enhance usability.
- Card-based design or table-based layout to display API keys and passwords in an organized manner.
- Subtle transitions and animations for user interactions (e.g., loading, saving).